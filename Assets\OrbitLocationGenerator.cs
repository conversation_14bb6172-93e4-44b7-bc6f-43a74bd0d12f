using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Finds and initializes existing orbit location GameObjects in the scene
/// </summary>
public class OrbitLocationInitializer : MonoBehaviour
{
    [Header("Orbit Initialization Settings")]
    [SerializeField] private bool initializeOnStart = true;

    [Header("Found Orbits")]
    [SerializeField] private List<GameObject> foundOrbits = new List<GameObject>();

    private void Start()
    {
        if (initializeOnStart)
        {
            InitializeExistingOrbitLocations();
        }
    }

    /// <summary>
    /// Find and initialize existing orbit locations in the scene
    /// </summary>
    [ContextMenu("Initialize Existing Orbit Locations")]
    public void InitializeExistingOrbitLocations()
    {
        foundOrbits.Clear();

        // Get WorldManager to find all celestial bodies
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found! Cannot initialize orbit locations.");
            return;
        }

        // Find all GameObjects that look like orbit locations
        GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);

        foreach (GameObject obj in allObjects)
        {
            if (IsOrbitLocation(obj))
            {
                InitializeOrbitLocation(obj);
                foundOrbits.Add(obj);
            }
        }

        Debug.Log($"Initialized {foundOrbits.Count} existing orbit locations");

        // Notify WorldManager to refresh orbit locations
        if (worldManager != null)
        {
            worldManager.RefreshOrbitLocations();
        }
    }

    /// <summary>
    /// Check if a GameObject is an orbit location based on naming convention
    /// Must match exact pattern: "Low[Planet]Orbit" (e.g., "LowMercuryOrbit")
    /// </summary>
    private bool IsOrbitLocation(GameObject obj)
    {
        if (obj == null) return false;

        string name = obj.name;

        // Must match exact pattern: "Low[Something]Orbit"
        return name.StartsWith("Low", System.StringComparison.Ordinal) &&
               name.EndsWith("Orbit", System.StringComparison.Ordinal) &&
               name.Length > 8; // "Low" + "Orbit" = 8 chars, need something in between
    }

    /// <summary>
    /// Initialize an existing orbit location GameObject
    /// </summary>
    private void InitializeOrbitLocation(GameObject orbitObject)
    {
        if (orbitObject == null) return;

        // Add OrbitLocation component if it doesn't exist
        if (!orbitObject.TryGetComponent<OrbitLocation>(out OrbitLocation orbitComponent))
        {
            orbitComponent = orbitObject.AddComponent<OrbitLocation>();
        }

        // Special case: Low Solar Orbit doesn't have a parent celestial body
        if (orbitObject.name == "LowSolarOrbit")
        {
            orbitComponent.Initialize(null); // No parent body for solar orbit
            Debug.Log($"Initialized special orbit location: {orbitObject.name} (No parent - solar orbit)");
            return;
        }

        // Try to find the parent celestial body for other orbits
        GameObject parentBody = FindParentCelestialBodyForOrbit(orbitObject);
        if (parentBody != null)
        {
            orbitComponent.Initialize(parentBody);
            Debug.Log($"Initialized orbit location: {orbitObject.name} (Parent: {parentBody.name})");
        }
        else
        {
            Debug.LogWarning($"Could not find parent celestial body for orbit: {orbitObject.name}");
        }
    }

    /// <summary>
    /// Find the parent celestial body for an orbit based on name matching
    /// Handles names like "LowMercuryOrbit" -> "Mercury"
    /// </summary>
    private GameObject FindParentCelestialBodyForOrbit(GameObject orbitObject)
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null) return null;

        string orbitName = orbitObject.name;
        List<GameObject> celestialBodies = worldManager.GetAllCelestialBodies();

        // Extract planet name from orbit name
        // "LowMercuryOrbit" -> "Mercury"
        string planetName = ExtractPlanetNameFromOrbit(orbitName);

        if (string.IsNullOrEmpty(planetName)) return null;

        // Try to match extracted planet name with celestial body name
        foreach (GameObject body in celestialBodies)
        {
            if (string.Equals(body.name, planetName, System.StringComparison.OrdinalIgnoreCase))
            {
                return body;
            }
        }

        return null;
    }

    /// <summary>
    /// Extract planet name from orbit name
    /// "LowMercuryOrbit" -> "Mercury"
    /// "LowEarthOrbit" -> "Earth"
    /// "LowSolarOrbit" -> null (special case - no parent celestial body)
    /// </summary>
    private string ExtractPlanetNameFromOrbit(string orbitName)
    {
        if (string.IsNullOrEmpty(orbitName)) return null;

        // Special case: Low Solar Orbit doesn't have a parent celestial body
        if (orbitName == "LowSolarOrbit")
        {
            return null;
        }

        // Remove "Low" prefix and "Orbit" suffix
        string result = orbitName;

        if (result.StartsWith("Low", System.StringComparison.OrdinalIgnoreCase))
        {
            result = result.Substring(3); // Remove "Low"
        }

        if (result.EndsWith("Orbit", System.StringComparison.OrdinalIgnoreCase))
        {
            result = result.Substring(0, result.Length - 5); // Remove "Orbit"
        }

        return result;
    }

    /// <summary>
    /// Get all found orbit locations
    /// </summary>
    public List<GameObject> GetFoundOrbits()
    {
        return new List<GameObject>(foundOrbits);
    }

    /// <summary>
    /// Find orbit location for a specific celestial body
    /// </summary>
    public GameObject FindOrbitForCelestialBody(GameObject celestialBody)
    {
        if (celestialBody == null) return null;

        string bodyName = celestialBody.name.ToLower();

        foreach (GameObject orbit in foundOrbits)
        {
            if (orbit != null && orbit.name.ToLower().Contains(bodyName))
            {
                return orbit;
            }
        }

        return null;
    }
}
